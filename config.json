{"camera": {"width": 320, "height": 240, "format_name": "FMT_BGR888"}, "serial": {"device": "/dev/ttyS0", "baudrate": 115200}, "protocol": {"frame_header": "$", "frame_footer": "\n", "red_laser_id": "R", "green_laser_id": "G", "rect_id": "M"}, "rect_detection": {"min_area": 5000, "max_area": 40000, "min_aspect_ratio": 0.2, "max_aspect_ratio": 5.0, "corner_threshold": 8, "detection_times": 10, "position_change_threshold": 10}, "laser_color": {"red_lower1": [0, 100, 50], "red_upper1": [10, 255, 255], "red_lower2": [160, 100, 50], "red_upper2": [180, 255, 255], "green_lower": [40, 100, 50], "green_upper": [80, 255, 255], "pixel_radius": 3}, "image_process": {"gaussian_kernel_size": [5, 5], "gaussian_sigma": 0, "canny_low_threshold": 50, "canny_high_threshold": 150, "morph_kernel_size": [3, 3], "morph_iterations": 1, "contour_approx_epsilon": 0.04}, "timing": {"serial_poll_interval_ms": 5}, "display": {"line_thickness": 2, "circle_radius": 5, "font_scale": 0.5, "font_thickness": 2, "outer_frame_color": [0, 0, 255], "inner_frame_color": [0, 255, 0], "middle_rect_color": [255, 0, 0], "red_laser_color": [0, 0, 255], "green_laser_color": [0, 255, 0]}}