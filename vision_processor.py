"""
视觉处理模块 - 矩形检测和激光检测算法
"""
from typing import List, Tuple, Optional, Union
from config import config
from logger import logger, measure_time

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    logger.warning("OpenCV不可用，视觉处理功能将受限", "VISION")


class RectangleDetector:
    """矩形检测器"""
    
    def __init__(self):
        """初始化矩形检测器"""
        self.detection_results = []  # 检测结果历史
        self.middle_rect_points = []  # 中间矩形的点
        
        # 从配置获取参数
        self.min_area = config.rect_detection.min_area
        self.max_area = config.rect_detection.max_area
        self.min_aspect_ratio = config.rect_detection.min_aspect_ratio
        self.max_aspect_ratio = config.rect_detection.max_aspect_ratio
        self.corner_threshold = config.rect_detection.corner_threshold
        self.detection_times = config.rect_detection.detection_times
        self.position_change_threshold = config.rect_detection.position_change_threshold
        
        logger.info("矩形检测器初始化完成", "RECT")
    
    @measure_time("detect_rectangles")
    def detect_rectangles(self, img) -> List:
        """
        检测矩形（与原代码逻辑完全一致）
        
        Args:
            img: 输入图像
            
        Returns:
            List: 检测到的矩形列表
        """
        if not CV2_AVAILABLE:
            logger.error("OpenCV不可用，无法进行矩形检测", "RECT")
            return []
        
        try:
            # 转换为灰度图像
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 高斯平滑滤波
            kernel_size = config.image_process.gaussian_kernel_size
            sigma = config.image_process.gaussian_sigma
            blurred = cv2.GaussianBlur(gray, kernel_size, sigma)
            
            # 调整边缘检测参数
            low_threshold = config.image_process.canny_low_threshold
            high_threshold = config.image_process.canny_high_threshold
            edged = cv2.Canny(blurred, low_threshold, high_threshold)
            
            # 定义膨胀核
            kernel_size = config.image_process.morph_kernel_size
            iterations = config.image_process.morph_iterations
            kernel = np.ones(kernel_size, np.uint8)
            # 对边缘图像进行膨胀操作
            dilated_edges = cv2.dilate(edged, kernel, iterations=iterations)
            
            # 查找轮廓，使用 RETR_TREE 模式来检测内外轮廓
            contours, hierarchy = cv2.findContours(dilated_edges.copy(), cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            
            # 矩形检测
            rectangles = []
            epsilon_factor = config.image_process.contour_approx_epsilon
            
            for i, contour in enumerate(contours):
                perimeter = cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon_factor * perimeter, True)
                if len(approx) == 4:
                    # 计算矩形的面积
                    area = cv2.contourArea(approx)
                    # 计算矩形的边界框
                    x, y, w, h = cv2.boundingRect(approx)
                    # 计算长宽比
                    aspect_ratio = float(w) / h if h != 0 else 0
                    # 过滤不符合条件的矩形
                    if self.min_area < area < self.max_area and self.min_aspect_ratio < aspect_ratio < self.max_aspect_ratio:
                        rectangles.append(approx)
            
            # 根据角点位置合并接近的矩形
            merged_rectangles = []
            for rect in rectangles:
                found_match = False
                for i, merged_rect in enumerate(merged_rectangles):
                    corner_matches = 0
                    for corner1 in rect:
                        for corner2 in merged_rect:
                            distance = np.linalg.norm(np.array(corner1[0]) - np.array(corner2[0]))
                            if distance < self.corner_threshold:
                                corner_matches += 1
                                break
                    if corner_matches >= 3:
                        found_match = True
                        if cv2.contourArea(rect) > cv2.contourArea(merged_rect):
                            merged_rectangles[i] = rect
                        break
                if not found_match:
                    merged_rectangles.append(rect)
            
            # 对合并后的矩形按面积排序
            merged_rectangles.sort(key=lambda r: cv2.contourArea(r), reverse=True)
            
            # 进一步过滤，只保留最大的两个矩形
            if len(merged_rectangles) > 2:
                merged_rectangles = merged_rectangles[:2]
            
            logger.debug(f"检测到 {len(merged_rectangles)} 个矩形", "RECT")
            return merged_rectangles
            
        except Exception as e:
            logger.error(f"矩形检测异常: {e}", "RECT")
            return []
    
    def is_position_stable(self, results: List) -> bool:
        """
        判断位置是否稳定（与原代码逻辑完全一致）
        
        Args:
            results: 检测结果列表
            
        Returns:
            bool: 位置是否稳定
        """
        if not CV2_AVAILABLE:
            return False
        
        try:
            if len(results) < self.detection_times:
                return False
            
            outer_rects = [result[0] for result in results]
            inner_rects = [result[1] for result in results]
            
            outer_corners = [rect.flatten() for rect in outer_rects]
            inner_corners = [rect.flatten() for rect in inner_rects]
            
            outer_std = np.std(outer_corners, axis=0)
            inner_std = np.std(inner_corners, axis=0)
            
            is_stable = np.all(outer_std < self.position_change_threshold) and np.all(inner_std < self.position_change_threshold)
            
            if is_stable:
                logger.info("矩形位置已稳定", "RECT")
            
            return is_stable
            
        except Exception as e:
            logger.error(f"位置稳定性检查异常: {e}", "RECT")
            return False
    
    def draw_middle_rect(self, outer_rect, inner_rect, img):
        """
        绘制中间矩形（与原代码逻辑完全一致）
        
        Args:
            outer_rect: 外矩形
            inner_rect: 内矩形
            img: 图像
            
        Returns:
            处理后的图像
        """
        if not CV2_AVAILABLE:
            logger.error("OpenCV不可用，无法绘制矩形", "RECT")
            return img
        
        try:
            outer_points = outer_rect.reshape(-1, 2)
            inner_points = inner_rect.reshape(-1, 2)
            
            middle_points = []
            for i in range(4):
                mid_point = ((outer_points[i][0] + inner_points[i][0]) // 2, 
                           (outer_points[i][1] + inner_points[i][1]) // 2)
                middle_points.append(mid_point)
            
            # 计算中点
            for i in range(4):
                next_i = (i + 1) % 4
                mid_edge_point = ((middle_points[i][0] + middle_points[next_i][0]) // 2, 
                                (middle_points[i][1] + middle_points[next_i][1]) // 2)
                middle_points.append(mid_edge_point)
            
            # 保存中间矩形点
            self.middle_rect_points = middle_points
            
            # 绘制中间矩形
            color = config.display.middle_rect_color
            thickness = config.display.line_thickness
            radius = config.display.circle_radius
            
            for i in range(4):
                next_i = (i + 1) % 4
                cv2.line(img, middle_points[i], middle_points[next_i], color, thickness)
            
            # 绘制角点和中点
            for point in middle_points:
                cv2.circle(img, point, radius, color, -1)
            
            logger.rect_points(middle_points)
            return img
            
        except Exception as e:
            logger.error(f"绘制中间矩形异常: {e}", "RECT")
            return img
    
    def get_middle_rect_points(self) -> List[Tuple[int, int]]:
        """获取中间矩形的点"""
        return self.middle_rect_points.copy()
    
    def add_detection_result(self, result):
        """添加检测结果到历史记录"""
        self.detection_results.append(result)
        # 保持历史记录在合理范围内
        if len(self.detection_results) > self.detection_times * 2:
            self.detection_results = self.detection_results[-self.detection_times:]
    
    def clear_detection_results(self):
        """清空检测结果历史"""
        self.detection_results.clear()
        logger.debug("检测结果历史已清空", "RECT")


class LaserDetector:
    """激光检测器"""
    
    def __init__(self):
        """初始化激光检测器"""
        self.pixel_radius = config.laser_color.pixel_radius
        
        # 获取激光颜色配置
        self.color_ranges = config.get_laser_color_arrays()
        
        logger.info("激光检测器初始化完成", "LASER")
    
    @measure_time("detect_lasers")
    def detect_lasers(self, img) -> Tuple[Optional[Tuple[int, int]], Optional[Tuple[int, int]]]:
        """
        检测激光点（与原代码逻辑完全一致）
        
        Args:
            img: 输入图像
            
        Returns:
            Tuple: (红色激光坐标, 绿色激光坐标)
        """
        if not CV2_AVAILABLE:
            logger.error("OpenCV不可用，无法进行激光检测", "LASER")
            return None, None
        
        try:
            # 初始化最亮激光点信息
            brightest_red_coords = None
            brightest_green_coords = None
            max_brightness_red = 0
            max_brightness_green = 0
            
            # 转换颜色空间为 HSV
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # 获取颜色范围
            ranges = self.color_ranges
            
            # 创建红色和绿色激光的二值化图像
            if CV2_AVAILABLE and 'red_lower1' in ranges:
                # 使用numpy数组或列表
                if hasattr(ranges['red_lower1'], 'dtype'):  # numpy数组
                    lower_red1 = ranges['red_lower1']
                    upper_red1 = ranges['red_upper1']
                    lower_red2 = ranges['red_lower2']
                    upper_red2 = ranges['red_upper2']
                    lower_green = ranges['green_lower']
                    upper_green = ranges['green_upper']
                else:  # 普通列表
                    lower_red1 = np.array(ranges['red_lower1'])
                    upper_red1 = np.array(ranges['red_upper1'])
                    lower_red2 = np.array(ranges['red_lower2'])
                    upper_red2 = np.array(ranges['red_upper2'])
                    lower_green = np.array(ranges['green_lower'])
                    upper_green = np.array(ranges['green_upper'])
                
                mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
                mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
                mask_red = cv2.bitwise_or(mask_red1, mask_red2)
                mask_green = cv2.inRange(hsv, lower_green, upper_green)
                
                # 闭运算
                kernel = np.ones((5, 5), np.uint8)
                mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, kernel)
                mask_green = cv2.morphologyEx(mask_green, cv2.MORPH_CLOSE, kernel)
                
                # 寻找红色激光外轮廓
                contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                for contour in contours_red:
                    rect = cv2.minAreaRect(contour)
                    laser_coords = tuple(map(int, rect[0]))
                    r_sum, g_sum = self.get_pixel_sum(img, laser_coords)
                    # 仅当红色分量大于绿色分量且亮度高于当前最大值时更新
                    if r_sum > g_sum and r_sum > max_brightness_red:
                        max_brightness_red = r_sum
                        brightest_red_coords = laser_coords
                
                # 寻找绿色激光外轮廓
                contours_green, _ = cv2.findContours(mask_green, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                for contour in contours_green:
                    rect = cv2.minAreaRect(contour)
                    laser_coords = tuple(map(int, rect[0]))
                    r_sum, g_sum = self.get_pixel_sum(img, laser_coords)
                    # 仅当绿色分量大于红色分量且亮度高于当前最大值时更新
                    if g_sum > r_sum and g_sum > max_brightness_green:
                        max_brightness_green = g_sum
                        brightest_green_coords = laser_coords
                
                # 绘制最亮的红色激光点
                if brightest_red_coords:
                    color = config.display.red_laser_color
                    radius = config.display.circle_radius
                    font_scale = config.display.font_scale
                    font_thickness = config.display.font_thickness
                    
                    cv2.circle(img, brightest_red_coords, radius, color, -1)
                    cv2.putText(img, "Red Laser", 
                              (brightest_red_coords[0], brightest_red_coords[1] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, font_thickness)
                
                # 绘制最亮的绿色激光点
                if brightest_green_coords:
                    color = config.display.green_laser_color
                    radius = config.display.circle_radius
                    font_scale = config.display.font_scale
                    font_thickness = config.display.font_thickness
                    
                    cv2.circle(img, brightest_green_coords, radius, color, -1)
                    cv2.putText(img, "Green Laser", 
                              (brightest_green_coords[0], brightest_green_coords[1] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, font_thickness)
            
            # 记录检测结果
            if brightest_red_coords:
                logger.laser_coords("Red", brightest_red_coords)
            if brightest_green_coords:
                logger.laser_coords("Green", brightest_green_coords)
            
            return brightest_red_coords, brightest_green_coords
            
        except Exception as e:
            logger.error(f"激光检测异常: {e}", "LASER")
            return None, None
    
    def get_pixel_sum(self, image, coords: Tuple[int, int]) -> Tuple[int, int]:
        """
        获取像素区域的R和G通道总值（与原代码逻辑完全一致）
        
        Args:
            image: 输入图像
            coords: 坐标点
            
        Returns:
            Tuple: (R通道总值, G通道总值)
        """
        try:
            # 获取图像宽度和高度
            height, width = image.shape[:2]
            radius = self.pixel_radius
            
            # 确定方圆的左上角和右下角坐标
            x, y = coords
            x_start = max(0, x - radius)
            y_start = max(0, y - radius)
            x_end = min(width - 1, x + radius)
            y_end = min(height - 1, y + radius)
            
            # 提取方圆区域
            roi = image[y_start:y_end, x_start:x_end]
            
            # 计算 R 和 G 通道总值
            # 选取红色
            r_channel = roi[:, :, 2]
            # 绿色
            g_channel = roi[:, :, 1]
            # 求和
            r_sum = int(r_channel.sum())
            g_sum = int(g_channel.sum())
            
            return r_sum, g_sum
            
        except Exception as e:
            logger.error(f"像素总值计算异常: {e}", "LASER")
            return 0, 0


# 创建全局检测器实例
rectangle_detector = RectangleDetector()
laser_detector = LaserDetector()
