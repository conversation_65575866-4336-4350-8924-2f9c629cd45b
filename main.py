"""
主控制器模块 - 系统状态机和业务逻辑管理
"""
import time
import signal
import sys
from enum import Enum
from typing import Optional, Tuple, List

# 导入配置和日志
from config import config
from logger import logger, measure_time

# 导入功能模块
from serial_comm import serial_comm
from vision_processor import rectangle_detector, laser_detector

# 尝试导入maix模块
try:
    from maix import image, display, app, camera
    MAIX_AVAILABLE = True
    logger.info("MaixCAM平台模块加载成功", "MAIN")
except ImportError:
    MAIX_AVAILABLE = False
    logger.warning("MaixCAM平台模块不可用，使用模拟模式", "MAIN")

# 尝试导入OpenCV
try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    logger.warning("OpenCV不可用，视觉功能将受限", "MAIN")


class SystemState(Enum):
    """系统状态枚举"""
    INIT = 0        # 初始化状态
    DETECTING = 1   # 矩形检测状态
    STABLE = 2      # 位置稳定状态
    RUNNING = 3     # 正常运行状态
    ERROR = 4       # 错误状态
    SHUTDOWN = 5    # 关闭状态


class VisionController:
    """视觉系统主控制器"""
    
    def __init__(self):
        """初始化主控制器"""
        self.state = SystemState.INIT
        self.running = False
        
        # 硬件组件
        self.camera = None
        self.display = None
        
        # 状态变量（与原代码保持一致）
        self.rect_detection_finished = False
        self.rect_sent = False
        self.send_red_next = True
        self.last_send_time = 0
        
        # 性能统计
        self.frame_count = 0
        self.start_time = time.time()
        self.last_fps_time = time.time()
        self.fps = 0
        
        logger.info("视觉控制器初始化完成", "MAIN")
    
    def _init_hardware(self) -> bool:
        """初始化硬件组件"""
        try:
            if MAIX_AVAILABLE:
                # 初始化显示器
                self.display = display.Display()
                logger.info("显示器初始化成功", "MAIN")
                
                # 初始化摄像头
                width = config.camera.width
                height = config.camera.height
                # 注意：这里需要根据实际的maix API调整格式参数
                self.camera = camera.Camera(width, height, image.Format.FMT_BGR888)
                logger.info(f"摄像头初始化成功: {width}x{height}", "MAIN")
                
                return True
            else:
                logger.info("硬件模拟模式", "MAIN")
                return True
                
        except Exception as e:
            logger.error(f"硬件初始化失败: {e}", "MAIN")
            self.state = SystemState.ERROR
            return False
    
    def _get_current_time_ms(self) -> int:
        """获取当前时间（毫秒）"""
        if MAIX_AVAILABLE:
            try:
                from maix import time as maix_time
                return maix_time.ticks_ms()
            except:
                pass
        return int(time.time() * 1000)
    
    @measure_time("capture_frame")
    def _capture_frame(self):
        """捕获图像帧"""
        if not self.camera:
            # 模拟模式：创建测试图像
            if CV2_AVAILABLE:
                img = np.zeros((config.camera.height, config.camera.width, 3), dtype=np.uint8)
                img[50:100, 50:100] = [0, 0, 255]  # 红色区域
                return img
            else:
                return None
        
        try:
            # 读取摄像头图像
            img = self.camera.read()
            
            # 转换为OpenCV格式
            if MAIX_AVAILABLE and hasattr(image, 'image2cv'):
                img = image.image2cv(img, ensure_bgr=False, copy=False)
            
            return img
            
        except Exception as e:
            logger.error(f"图像捕获失败: {e}", "MAIN")
            return None
    
    def _process_rectangle_detection(self, img) -> bool:
        """处理矩形检测逻辑"""
        if self.rect_detection_finished:
            return True
        
        try:
            # 检测矩形
            merged_rectangles = rectangle_detector.detect_rectangles(img)
            
            if len(merged_rectangles) == 2:
                # 添加检测结果到历史
                rectangle_detector.add_detection_result(merged_rectangles)
                
                # 检查位置稳定性
                if rectangle_detector.is_position_stable(rectangle_detector.detection_results):
                    outer_rect = merged_rectangles[0]
                    inner_rect = merged_rectangles[1]
                    
                    # 绘制中间矩形
                    img = rectangle_detector.draw_middle_rect(outer_rect, inner_rect, img)
                    
                    # 标记检测完成
                    self.rect_detection_finished = True
                    self.state = SystemState.STABLE
                    
                    # 发送矩形坐标数据（仅发送一次）
                    if not self.rect_sent:
                        middle_points = rectangle_detector.get_middle_rect_points()
                        if middle_points:
                            serial_comm.send_rectangle_data(middle_points)
                            self.rect_sent = True
                            logger.info("矩形坐标已发送", "MAIN")
                    
                    return True
                else:
                    # 位置未稳定，绘制检测框
                    self._draw_detection_frames(img, merged_rectangles)
                    
            elif len(merged_rectangles) == 1:
                # 只检测到一个矩形
                self._draw_single_frame(img, merged_rectangles[0])
            
            return False
            
        except Exception as e:
            logger.error(f"矩形检测处理异常: {e}", "MAIN")
            return False
    
    def _draw_detection_frames(self, img, rectangles):
        """绘制检测框"""
        if not CV2_AVAILABLE or len(rectangles) != 2:
            return
        
        try:
            outer_rect = rectangles[0]
            inner_rect = rectangles[1]
            
            # 绘制外框和内框
            outer_color = config.display.outer_frame_color
            inner_color = config.display.inner_frame_color
            thickness = config.display.line_thickness
            font_scale = config.display.font_scale
            font_thickness = config.display.font_thickness
            
            cv2.drawContours(img, [outer_rect], -1, outer_color, thickness)
            cv2.putText(img, "Outer Frame", 
                       (outer_rect[0][0][0], outer_rect[0][0][1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, outer_color, font_thickness)
            
            cv2.drawContours(img, [inner_rect], -1, inner_color, thickness)
            cv2.putText(img, "Inner Frame", 
                       (inner_rect[0][0][0], inner_rect[0][0][1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, inner_color, font_thickness)
                       
        except Exception as e:
            logger.error(f"绘制检测框异常: {e}", "MAIN")
    
    def _draw_single_frame(self, img, rect):
        """绘制单个检测框"""
        if not CV2_AVAILABLE:
            return
        
        try:
            # 根据面积判断是外框还是内框
            area = cv2.contourArea(rect)
            threshold = (config.rect_detection.max_area + config.rect_detection.min_area) / 2
            
            if area > threshold:
                color = config.display.outer_frame_color
                label = "Outer Frame"
            else:
                color = config.display.inner_frame_color
                label = "Inner Frame"
            
            thickness = config.display.line_thickness
            font_scale = config.display.font_scale
            font_thickness = config.display.font_thickness
            
            cv2.drawContours(img, [rect], -1, color, thickness)
            cv2.putText(img, label, (rect[0][0][0], rect[0][0][1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, font_thickness)
                       
        except Exception as e:
            logger.error(f"绘制单个框异常: {e}", "MAIN")
    
    def _draw_middle_rect_points(self, img):
        """绘制中间矩形的点"""
        if not CV2_AVAILABLE:
            return
        
        try:
            middle_points = rectangle_detector.get_middle_rect_points()
            color = config.display.middle_rect_color
            radius = config.display.circle_radius
            
            for point in middle_points:
                cv2.circle(img, point, radius, color, -1)
                
        except Exception as e:
            logger.error(f"绘制中间矩形点异常: {e}", "MAIN")
    
    def _process_laser_detection(self, img):
        """处理激光检测和发送逻辑"""
        try:
            # 检测激光
            red_laser_coords, green_laser_coords = laser_detector.detect_lasers(img)
            
            # 检查是否达到发送间隔时间
            current_time_ms = self._get_current_time_ms()
            if current_time_ms - self.last_send_time >= config.timing.serial_poll_interval_ms:
                # 交替发送红色和绿色激光坐标（与原代码逻辑完全一致）
                if self.send_red_next:
                    if red_laser_coords:
                        serial_comm.send_red_laser(red_laser_coords)
                        self.last_send_time = current_time_ms
                        self.send_red_next = False
                    elif green_laser_coords:  # 如果没有红色激光，但有绿色激光，则发送绿色
                        serial_comm.send_green_laser(green_laser_coords)
                        self.last_send_time = current_time_ms
                        self.send_red_next = True
                else:
                    if green_laser_coords:
                        serial_comm.send_green_laser(green_laser_coords)
                        self.last_send_time = current_time_ms
                        self.send_red_next = True
                    elif red_laser_coords:  # 如果没有绿色激光，但有红色激光，则发送红色
                        serial_comm.send_red_laser(red_laser_coords)
                        self.last_send_time = current_time_ms
                        self.send_red_next = False
                        
        except Exception as e:
            logger.error(f"激光检测处理异常: {e}", "MAIN")
    
    def _display_frame(self, img):
        """显示图像帧"""
        try:
            if self.display and MAIX_AVAILABLE:
                # 转换为maix显示格式
                if hasattr(image, 'cv2image'):
                    img_show = image.cv2image(img, bgr=True, copy=False)
                    self.display.show(img_show)
            else:
                # 模拟模式，仅记录日志
                logger.debug("模拟显示图像帧", "MAIN")
                
        except Exception as e:
            logger.error(f"图像显示异常: {e}", "MAIN")
    
    def _update_fps(self):
        """更新FPS统计"""
        self.frame_count += 1
        current_time = time.time()
        
        if current_time - self.last_fps_time >= 1.0:  # 每秒更新一次
            self.fps = self.frame_count / (current_time - self.last_fps_time)
            logger.debug(f"FPS: {self.fps:.2f}", "PERF")
            self.frame_count = 0
            self.last_fps_time = current_time
    
    def _should_exit(self) -> bool:
        """检查是否应该退出"""
        if MAIX_AVAILABLE:
            try:
                return app.need_exit()
            except:
                pass
        return not self.running
    
    @measure_time("main_loop_iteration")
    def _main_loop_iteration(self):
        """主循环单次迭代"""
        # 捕获图像
        img = self._capture_frame()
        if img is None:
            logger.warning("图像捕获失败", "MAIN")
            return
        
        # 处理矩形检测
        if not self.rect_detection_finished:
            self._process_rectangle_detection(img)
        else:
            # 绘制中间矩形的点
            self._draw_middle_rect_points(img)
        
        # 处理激光检测
        self._process_laser_detection(img)
        
        # 显示图像
        self._display_frame(img)
        
        # 更新性能统计
        self._update_fps()
    
    def start(self):
        """启动系统"""
        logger.info("启动视觉系统", "MAIN")
        
        # 初始化硬件
        if not self._init_hardware():
            logger.error("硬件初始化失败，系统启动中止", "MAIN")
            return False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.running = True
        self.state = SystemState.DETECTING
        self.start_time = time.time()
        
        logger.info("系统启动成功，进入主循环", "MAIN")
        
        try:
            # 主循环
            while not self._should_exit():
                self._main_loop_iteration()
                
        except KeyboardInterrupt:
            logger.info("接收到中断信号", "MAIN")
        except Exception as e:
            logger.error(f"主循环异常: {e}", "MAIN")
            self.state = SystemState.ERROR
        finally:
            self.stop()
        
        return True
    
    def stop(self):
        """停止系统"""
        logger.info("正在停止视觉系统", "MAIN")
        
        self.running = False
        self.state = SystemState.SHUTDOWN
        
        # 关闭串口通信
        try:
            serial_comm.close()
        except Exception as e:
            logger.error(f"关闭串口通信异常: {e}", "MAIN")
        
        # 打印性能统计
        self._print_performance_stats()
        
        logger.info("视觉系统已停止", "MAIN")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，准备退出", "MAIN")
        self.running = False
    
    def _print_performance_stats(self):
        """打印性能统计"""
        runtime = time.time() - self.start_time
        logger.info("=== 性能统计报告 ===", "PERF")
        logger.info(f"运行时间: {runtime:.2f} 秒", "PERF")
        logger.info(f"平均FPS: {self.fps:.2f}", "PERF")
        logger.info(f"系统状态: {self.state.name}", "PERF")
        logger.info(f"矩形检测完成: {self.rect_detection_finished}", "PERF")
        logger.info(f"矩形坐标已发送: {self.rect_sent}", "PERF")
        
        # 打印串口统计
        serial_comm.print_stats()
        
        # 打印日志性能统计
        logger.print_performance_stats()
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        return {
            'state': self.state.name,
            'running': self.running,
            'rect_detection_finished': self.rect_detection_finished,
            'rect_sent': self.rect_sent,
            'fps': self.fps,
            'runtime': time.time() - self.start_time,
            'serial_stats': serial_comm.get_stats()
        }


def main():
    """主函数"""
    logger.info("=== 视觉系统启动 ===", "MAIN")
    
    # 创建控制器
    controller = VisionController()
    
    # 启动系统
    try:
        controller.start()
    except Exception as e:
        logger.error(f"系统启动失败: {e}", "MAIN")
        return 1
    
    logger.info("=== 视觉系统退出 ===", "MAIN")
    return 0


if __name__ == "__main__":
    sys.exit(main())
