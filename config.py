"""
配置管理模块 - 统一管理所有硬编码参数
"""
import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class CameraConfig:
    """摄像头配置"""
    width: int = 320
    height: int = 240
    format_name: str = "FMT_BGR888"  # maix.image.Format.FMT_BGR888


@dataclass
class SerialConfig:
    """串口配置"""
    device: str = "/dev/ttyS0"
    baudrate: int = 115200


@dataclass
class ProtocolConfig:
    """通信协议配置"""
    frame_header: str = "$"
    frame_footer: str = "\n"
    red_laser_id: str = "R"
    green_laser_id: str = "G"
    rect_id: str = "M"


@dataclass
class RectDetectionConfig:
    """矩形检测参数配置"""
    min_area: int = 5000
    max_area: int = 40000
    min_aspect_ratio: float = 0.2
    max_aspect_ratio: float = 5.0
    corner_threshold: int = 8
    detection_times: int = 10
    position_change_threshold: int = 10


@dataclass
class LaserColorConfig:
    """激光颜色检测配置"""
    # 红色激光HSV范围
    red_lower1: list = None
    red_upper1: list = None
    red_lower2: list = None
    red_upper2: list = None
    # 绿色激光HSV范围
    green_lower: list = None
    green_upper: list = None
    # 像素检测半径
    pixel_radius: int = 3
    
    def __post_init__(self):
        if self.red_lower1 is None:
            self.red_lower1 = [0, 100, 50]
        if self.red_upper1 is None:
            self.red_upper1 = [10, 255, 255]
        if self.red_lower2 is None:
            self.red_lower2 = [160, 100, 50]
        if self.red_upper2 is None:
            self.red_upper2 = [180, 255, 255]
        if self.green_lower is None:
            self.green_lower = [40, 100, 50]
        if self.green_upper is None:
            self.green_upper = [80, 255, 255]


@dataclass
class ImageProcessConfig:
    """图像处理参数配置"""
    # 高斯滤波参数
    gaussian_kernel_size: tuple = (5, 5)
    gaussian_sigma: int = 0
    # Canny边缘检测参数
    canny_low_threshold: int = 50
    canny_high_threshold: int = 150
    # 形态学操作参数
    morph_kernel_size: tuple = (3, 3)
    morph_iterations: int = 1
    # 轮廓近似参数
    contour_approx_epsilon: float = 0.04


@dataclass
class TimingConfig:
    """时序控制配置"""
    serial_poll_interval_ms: int = 5


@dataclass
class DisplayConfig:
    """显示配置"""
    # 绘制参数
    line_thickness: int = 2
    circle_radius: int = 5
    font_scale: float = 0.5
    font_thickness: int = 2
    # 颜色配置 (BGR格式)
    outer_frame_color: tuple = (0, 0, 255)  # 红色
    inner_frame_color: tuple = (0, 255, 0)  # 绿色
    middle_rect_color: tuple = (255, 0, 0)  # 蓝色
    red_laser_color: tuple = (0, 0, 255)    # 红色
    green_laser_color: tuple = (0, 255, 0)  # 绿色


class Config:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.camera = CameraConfig()
        self.serial = SerialConfig()
        self.protocol = ProtocolConfig()
        self.rect_detection = RectDetectionConfig()
        self.laser_color = LaserColorConfig()
        self.image_process = ImageProcessConfig()
        self.timing = TimingConfig()
        self.display = DisplayConfig()
        
        # 加载配置文件
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置
                self._update_from_dict(config_data)
                return True
            else:
                # 创建默认配置文件
                self.save_config()
                return True
        except Exception as e:
            print(f"配置加载失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            config_data = {
                'camera': asdict(self.camera),
                'serial': asdict(self.serial),
                'protocol': asdict(self.protocol),
                'rect_detection': asdict(self.rect_detection),
                'laser_color': asdict(self.laser_color),
                'image_process': asdict(self.image_process),
                'timing': asdict(self.timing),
                'display': asdict(self.display)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"配置保存失败: {e}")
            return False
    
    def _update_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        if 'camera' in config_data:
            self.camera = CameraConfig(**config_data['camera'])
        if 'serial' in config_data:
            self.serial = SerialConfig(**config_data['serial'])
        if 'protocol' in config_data:
            self.protocol = ProtocolConfig(**config_data['protocol'])
        if 'rect_detection' in config_data:
            self.rect_detection = RectDetectionConfig(**config_data['rect_detection'])
        if 'laser_color' in config_data:
            self.laser_color = LaserColorConfig(**config_data['laser_color'])
        if 'image_process' in config_data:
            self.image_process = ImageProcessConfig(**config_data['image_process'])
        if 'timing' in config_data:
            self.timing = TimingConfig(**config_data['timing'])
        if 'display' in config_data:
            self.display = DisplayConfig(**config_data['display'])
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证摄像头配置
            assert self.camera.width > 0 and self.camera.height > 0, "摄像头分辨率必须大于0"
            
            # 验证串口配置
            assert self.serial.baudrate > 0, "串口波特率必须大于0"
            # 跳过串口设备路径验证（在不同平台上路径不同）
            
            # 验证矩形检测参数
            assert self.rect_detection.min_area < self.rect_detection.max_area, "最小面积必须小于最大面积"
            assert 0 < self.rect_detection.min_aspect_ratio < self.rect_detection.max_aspect_ratio, "长宽比范围无效"
            
            # 验证激光颜色范围
            for color_range in [self.laser_color.red_lower1, self.laser_color.red_upper1,
                               self.laser_color.red_lower2, self.laser_color.red_upper2,
                               self.laser_color.green_lower, self.laser_color.green_upper]:
                assert len(color_range) == 3, "HSV颜色范围必须包含3个值"
                assert all(0 <= v <= 255 for v in color_range), "HSV值必须在0-255范围内"
            
            return True
        except AssertionError as e:
            print(f"配置验证失败: {e}")
            return False
        except Exception as e:
            print(f"配置验证异常: {e}")
            return False
    
    def get_laser_color_arrays(self):
        """获取激光颜色检测的数组（兼容numpy）"""
        try:
            import numpy as np
            return {
                'red_lower1': np.array(self.laser_color.red_lower1),
                'red_upper1': np.array(self.laser_color.red_upper1),
                'red_lower2': np.array(self.laser_color.red_lower2),
                'red_upper2': np.array(self.laser_color.red_upper2),
                'green_lower': np.array(self.laser_color.green_lower),
                'green_upper': np.array(self.laser_color.green_upper)
            }
        except ImportError:
            # 如果没有numpy，返回普通列表
            return {
                'red_lower1': self.laser_color.red_lower1,
                'red_upper1': self.laser_color.red_upper1,
                'red_lower2': self.laser_color.red_lower2,
                'red_upper2': self.laser_color.red_upper2,
                'green_lower': self.laser_color.green_lower,
                'green_upper': self.laser_color.green_upper
            }


# 全局配置实例
config = Config()
