"""
日志管理模块 - 分级日志系统和性能监控
"""
import os
import time
import functools
from datetime import datetime
from typing import Optional, Callable, Any
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = 0
    INFO = 1
    WARNING = 2
    ERROR = 3


class Logger:
    """日志管理器"""
    
    def __init__(self, 
                 name: str = "VisionSystem",
                 log_level: LogLevel = LogLevel.INFO,
                 log_file: Optional[str] = "vision_system.log",
                 max_file_size: int = 1024 * 1024,  # 1MB
                 backup_count: int = 3,
                 console_output: bool = True):
        """
        初始化日志器
        
        Args:
            name: 日志器名称
            log_level: 日志级别
            log_file: 日志文件路径，None表示不写文件
            max_file_size: 最大文件大小（字节）
            backup_count: 备份文件数量
            console_output: 是否输出到控制台
        """
        self.name = name
        self.log_level = log_level
        self.log_file = log_file
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.console_output = console_output
        
        # 性能统计
        self.performance_stats = {}
        
        # 确保日志目录存在
        if self.log_file:
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir)
                except OSError:
                    pass  # 忽略创建失败，可能是权限问题
    
    def _format_message(self, level: LogLevel, module: str, message: str) -> str:
        """格式化日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        return f"[{timestamp}][{level.name}][{module}] {message}"
    
    def _should_log(self, level: LogLevel) -> bool:
        """判断是否应该记录此级别的日志"""
        return level.value >= self.log_level.value
    
    def _write_to_file(self, formatted_message: str):
        """写入日志文件"""
        if not self.log_file:
            return
            
        try:
            # 检查文件大小，如果超过限制则轮转
            if os.path.exists(self.log_file) and os.path.getsize(self.log_file) > self.max_file_size:
                self._rotate_log_file()
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(formatted_message + '\n')
                f.flush()
        except Exception:
            pass  # 忽略文件写入错误，避免影响主程序
    
    def _rotate_log_file(self):
        """轮转日志文件"""
        try:
            # 删除最老的备份文件
            oldest_backup = f"{self.log_file}.{self.backup_count}"
            if os.path.exists(oldest_backup):
                os.remove(oldest_backup)
            
            # 重命名现有备份文件
            for i in range(self.backup_count - 1, 0, -1):
                old_file = f"{self.log_file}.{i}"
                new_file = f"{self.log_file}.{i + 1}"
                if os.path.exists(old_file):
                    os.rename(old_file, new_file)
            
            # 重命名当前日志文件
            if os.path.exists(self.log_file):
                os.rename(self.log_file, f"{self.log_file}.1")
        except Exception:
            pass  # 忽略轮转错误
    
    def _log(self, level: LogLevel, module: str, message: str):
        """内部日志方法"""
        if not self._should_log(level):
            return
        
        formatted_message = self._format_message(level, module, message)
        
        # 输出到控制台
        if self.console_output:
            print(formatted_message)
        
        # 写入文件
        self._write_to_file(formatted_message)
    
    def debug(self, message: str, module: str = "MAIN"):
        """调试日志"""
        self._log(LogLevel.DEBUG, module, message)
    
    def info(self, message: str, module: str = "MAIN"):
        """信息日志"""
        self._log(LogLevel.INFO, module, message)
    
    def warning(self, message: str, module: str = "MAIN"):
        """警告日志"""
        self._log(LogLevel.WARNING, module, message)
    
    def error(self, message: str, module: str = "MAIN"):
        """错误日志"""
        self._log(LogLevel.ERROR, module, message)
    
    def serial_send(self, data: str):
        """串口发送专用日志"""
        self.debug(f"串口发送: {data}", "SERIAL")
    
    def laser_coords(self, laser_type: str, coords: tuple):
        """激光坐标专用日志"""
        self.debug(f"{laser_type} laser coordinates: {coords}", "LASER")
    
    def rect_points(self, points: list):
        """矩形点专用日志"""
        self.info(f"Middle rectangle points: {points}", "RECT")
    
    def processing_time(self, duration_ms: float):
        """处理时间专用日志"""
        self.debug(f"Processing time: {duration_ms:.2f}ms", "PERF")
    
    def measure_time(self, func_name: str = None):
        """性能监控装饰器"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                name = func_name or func.__name__
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = (time.time() - start_time) * 1000  # 转换为毫秒
                    
                    # 更新性能统计
                    if name not in self.performance_stats:
                        self.performance_stats[name] = {
                            'count': 0,
                            'total_time': 0,
                            'avg_time': 0,
                            'max_time': 0,
                            'min_time': float('inf')
                        }
                    
                    stats = self.performance_stats[name]
                    stats['count'] += 1
                    stats['total_time'] += duration
                    stats['avg_time'] = stats['total_time'] / stats['count']
                    stats['max_time'] = max(stats['max_time'], duration)
                    stats['min_time'] = min(stats['min_time'], duration)
                    
                    # 记录性能日志
                    self.debug(f"{name} 执行时间: {duration:.2f}ms", "PERF")
            
            return wrapper
        return decorator
    
    def get_performance_stats(self) -> dict:
        """获取性能统计信息"""
        return self.performance_stats.copy()
    
    def print_performance_stats(self):
        """打印性能统计信息"""
        if not self.performance_stats:
            self.info("暂无性能统计数据", "PERF")
            return
        
        self.info("=== 性能统计报告 ===", "PERF")
        for func_name, stats in self.performance_stats.items():
            self.info(f"{func_name}: 调用{stats['count']}次, "
                     f"平均{stats['avg_time']:.2f}ms, "
                     f"最大{stats['max_time']:.2f}ms, "
                     f"最小{stats['min_time']:.2f}ms", "PERF")
    
    def set_level(self, level: LogLevel):
        """设置日志级别"""
        self.log_level = level
        self.info(f"日志级别设置为: {level.name}", "LOGGER")


# 创建全局日志实例
logger = Logger(
    name="VisionSystem",
    log_level=LogLevel.INFO,
    log_file="logs/vision_system.log",
    max_file_size=512 * 1024,  # 512KB，适合嵌入式环境
    backup_count=2,
    console_output=True
)


# 便捷函数
def debug(message: str, module: str = "MAIN"):
    """调试日志便捷函数"""
    logger.debug(message, module)


def info(message: str, module: str = "MAIN"):
    """信息日志便捷函数"""
    logger.info(message, module)


def warning(message: str, module: str = "MAIN"):
    """警告日志便捷函数"""
    logger.warning(message, module)


def error(message: str, module: str = "MAIN"):
    """错误日志便捷函数"""
    logger.error(message, module)


def measure_time(func_name: str = None):
    """性能监控装饰器便捷函数"""
    return logger.measure_time(func_name)
