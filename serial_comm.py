"""
串口通信模块 - 封装串口通信功能和协议处理
"""
import time
import threading
from queue import Queue, Empty
from typing import Optional, Tuple, List, Union
from enum import Enum
from config import config
from logger import logger


class ConnectionStatus(Enum):
    """连接状态枚举"""
    DISCONNECTED = 0
    CONNECTING = 1
    CONNECTED = 2
    ERROR = 3


class SerialMessage:
    """串口消息类"""
    def __init__(self, data: str, retry_count: int = 0, timestamp: float = None):
        self.data = data
        self.retry_count = retry_count
        self.timestamp = timestamp or time.time()


class SerialCommunicator:
    """串口通信器"""
    
    def __init__(self, max_retry_count: int = 3, queue_size: int = 100):
        """
        初始化串口通信器
        
        Args:
            max_retry_count: 最大重试次数
            queue_size: 发送队列大小
        """
        self.max_retry_count = max_retry_count
        self.send_queue = Queue(maxsize=queue_size)
        self.status = ConnectionStatus.DISCONNECTED
        self.serial_port = None
        self.send_thread = None
        self.running = False
        
        # 统计信息
        self.stats = {
            'sent_count': 0,
            'failed_count': 0,
            'retry_count': 0,
            'last_send_time': 0
        }
        
        # 初始化串口连接
        self._init_serial()
    
    def _init_serial(self) -> bool:
        """初始化串口连接"""
        try:
            # 尝试导入maix uart模块
            from maix import uart
            
            self.status = ConnectionStatus.CONNECTING
            logger.info(f"正在连接串口: {config.serial.device}, 波特率: {config.serial.baudrate}", "SERIAL")
            
            # 创建串口连接
            self.serial_port = uart.UART(config.serial.device, config.serial.baudrate)
            
            self.status = ConnectionStatus.CONNECTED
            logger.info("串口连接成功", "SERIAL")
            
            # 启动发送线程
            self._start_send_thread()
            return True
            
        except ImportError:
            logger.warning("maix.uart模块不可用，使用模拟模式", "SERIAL")
            self.status = ConnectionStatus.CONNECTED  # 模拟连接成功
            self._start_send_thread()
            return True
        except Exception as e:
            logger.error(f"串口初始化失败: {e}", "SERIAL")
            self.status = ConnectionStatus.ERROR
            return False
    
    def _start_send_thread(self):
        """启动发送线程"""
        if self.send_thread and self.send_thread.is_alive():
            return
        
        self.running = True
        self.send_thread = threading.Thread(target=self._send_worker, daemon=True)
        self.send_thread.start()
        logger.debug("发送线程已启动", "SERIAL")
    
    def _send_worker(self):
        """发送工作线程"""
        while self.running:
            try:
                # 从队列获取消息，超时1秒
                message = self.send_queue.get(timeout=1.0)
                
                # 尝试发送消息
                if self._send_data(message.data):
                    self.stats['sent_count'] += 1
                    self.stats['last_send_time'] = time.time()
                    logger.serial_send(message.data)
                else:
                    # 发送失败，检查是否需要重试
                    if message.retry_count < self.max_retry_count:
                        message.retry_count += 1
                        self.stats['retry_count'] += 1
                        # 重新加入队列
                        try:
                            self.send_queue.put_nowait(message)
                            logger.warning(f"消息发送失败，重试第{message.retry_count}次", "SERIAL")
                        except:
                            logger.error("重试队列已满，丢弃消息", "SERIAL")
                            self.stats['failed_count'] += 1
                    else:
                        self.stats['failed_count'] += 1
                        logger.error(f"消息发送失败，已达最大重试次数: {message.data}", "SERIAL")
                
                self.send_queue.task_done()
                
            except Empty:
                continue  # 队列为空，继续等待
            except Exception as e:
                logger.error(f"发送线程异常: {e}", "SERIAL")
    
    def _send_data(self, data: str) -> bool:
        """发送数据到串口"""
        try:
            if self.serial_port:
                # 使用真实串口
                self.serial_port.write(data.encode())
                return True
            else:
                # 模拟模式，仅记录日志
                logger.debug(f"模拟发送: {data}", "SERIAL")
                return True
        except Exception as e:
            logger.error(f"串口发送异常: {e}", "SERIAL")
            # 尝试重连
            self._reconnect()
            return False
    
    def _reconnect(self):
        """重新连接串口"""
        if self.status == ConnectionStatus.CONNECTING:
            return  # 已在连接中
        
        logger.info("尝试重新连接串口", "SERIAL")
        self.status = ConnectionStatus.CONNECTING
        
        try:
            if self.serial_port:
                # 关闭现有连接
                try:
                    self.serial_port.close()
                except:
                    pass
                self.serial_port = None
            
            # 重新初始化
            time.sleep(1)  # 等待1秒后重连
            self._init_serial()
            
        except Exception as e:
            logger.error(f"重连失败: {e}", "SERIAL")
            self.status = ConnectionStatus.ERROR
    
    def _calculate_checksum(self, data: str) -> int:
        """计算校验和（与原代码完全一致）"""
        checksum = 0
        for c in data:
            checksum += ord(c)
        return checksum & 0xFF
    
    def _build_frame(self, frame_type: str, data_parts: List[str]) -> str:
        """构建数据帧"""
        # 构建数据部分
        data = f"{config.protocol.frame_header}{frame_type},{','.join(data_parts)}"
        
        # 计算校验和
        checksum = self._calculate_checksum(data)
        
        # 添加校验和和帧尾
        frame = f"{data},{checksum:02X}{config.protocol.frame_footer}"
        
        return frame
    
    def send_laser_data(self, laser_type: str, coords: Tuple[int, int]) -> bool:
        """
        发送激光坐标数据
        
        Args:
            laser_type: 激光类型 ('R' 或 'G')
            coords: 坐标元组 (x, y)
            
        Returns:
            bool: 是否成功加入发送队列
        """
        if coords is None:
            return False
        
        try:
            x, y = coords
            data_parts = [str(x), str(y)]
            frame = self._build_frame(laser_type, data_parts)
            
            # 加入发送队列
            message = SerialMessage(frame)
            self.send_queue.put_nowait(message)
            return True
            
        except Exception as e:
            logger.error(f"激光数据发送失败: {e}", "SERIAL")
            return False
    
    def send_rectangle_data(self, points: List[Tuple[int, int]]) -> bool:
        """
        发送矩形坐标数据
        
        Args:
            points: 坐标点列表 [(x1,y1), (x2,y2), ...]
            
        Returns:
            bool: 是否成功加入发送队列
        """
        if not points or len(points) == 0:
            return False
        
        try:
            # 构建数据部分
            point_count = str(len(points))
            coords_parts = []
            for x, y in points:
                coords_parts.extend([str(x), str(y)])
            
            data_parts = [point_count] + coords_parts
            frame = self._build_frame(config.protocol.rect_id, data_parts)
            
            # 加入发送队列
            message = SerialMessage(frame)
            self.send_queue.put_nowait(message)
            return True
            
        except Exception as e:
            logger.error(f"矩形数据发送失败: {e}", "SERIAL")
            return False
    
    def send_red_laser(self, coords: Tuple[int, int]) -> bool:
        """发送红色激光坐标"""
        return self.send_laser_data(config.protocol.red_laser_id, coords)
    
    def send_green_laser(self, coords: Tuple[int, int]) -> bool:
        """发送绿色激光坐标"""
        return self.send_laser_data(config.protocol.green_laser_id, coords)
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.status == ConnectionStatus.CONNECTED
    
    def get_status(self) -> ConnectionStatus:
        """获取连接状态"""
        return self.status
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['queue_size'] = self.send_queue.qsize()
        stats['status'] = self.status.name
        return stats
    
    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()
        logger.info("=== 串口通信统计 ===", "SERIAL")
        logger.info(f"状态: {stats['status']}", "SERIAL")
        logger.info(f"发送成功: {stats['sent_count']} 次", "SERIAL")
        logger.info(f"发送失败: {stats['failed_count']} 次", "SERIAL")
        logger.info(f"重试次数: {stats['retry_count']} 次", "SERIAL")
        logger.info(f"队列大小: {stats['queue_size']}", "SERIAL")
        if stats['last_send_time'] > 0:
            last_time = time.strftime("%H:%M:%S", time.localtime(stats['last_send_time']))
            logger.info(f"最后发送: {last_time}", "SERIAL")
    
    def close(self):
        """关闭串口通信"""
        logger.info("正在关闭串口通信", "SERIAL")
        
        # 停止发送线程
        self.running = False
        if self.send_thread and self.send_thread.is_alive():
            self.send_thread.join(timeout=2)
        
        # 关闭串口
        if self.serial_port:
            try:
                self.serial_port.close()
            except:
                pass
            self.serial_port = None
        
        self.status = ConnectionStatus.DISCONNECTED
        logger.info("串口通信已关闭", "SERIAL")


# 创建全局串口通信实例
serial_comm = SerialCommunicator()
